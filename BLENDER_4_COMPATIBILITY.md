# Blender 4.4.3 兼容性更新

## 概述

这个MHW Mod3 Import-Exporter插件原本是为Blender 2.79编写的，现已更新以支持Blender 4.4.3。

## 主要修改

### 1. 插件信息更新
- 更新了`bl_info`中的Blender版本要求从`(2, 80, 0)`到`(4, 0, 0)`
- 版本号从`(2,0,1)`更新到`(2,0,2)`

### 2. 对象选择API更新
**旧API (已弃用):**
```python
obj.select = True/False
```

**新API (Blender 4.x):**
```python
obj.select_set(True/False)
```

**修改的文件:**
- `blender/BlenderMod3Importer.py`
- `blender/BlenderMod3Exporter.py`
- `operators/mod3export.py`

### 3. 活动对象API更新
**旧API (已弃用):**
```python
bpy.context.scene.objects.active = obj
```

**新API (Blender 4.x):**
```python
bpy.context.view_layer.objects.active = obj
```

**修改的文件:**
- `blender/BlenderMod3Importer.py`
- `blender/BlenderMod3Exporter.py`

### 4. 对象链接API更新
**旧API (已弃用):**
```python
bpy.context.scene.objects.link(obj)
```

**新API (Blender 4.x):**
```python
bpy.context.collection.objects.link(obj)
```

**修改的文件:**
- `blender/BlenderMod3Exporter.py`

## 兼容性测试

包含了一个测试脚本`test_compatibility.py`来验证插件与Blender 4.4.3的兼容性。

### 运行测试
在Blender的脚本编辑器中运行测试脚本：
```python
exec(open("test_compatibility.py").read())
```

### 测试内容
1. 插件注册测试
2. 导入操作符可用性测试
3. 导出操作符可用性测试
4. 菜单集成测试
5. Blender API兼容性测试

## 安装说明

1. 下载整个插件文件夹
2. 在Blender中，转到 Edit > Preferences > Add-ons
3. 点击"Install..."按钮
4. 选择插件文件夹或压缩包
5. 启用"MHW Mod3 Model Importer"插件

## 使用方法

### 导入MOD3文件
1. File > Import > MHW MOD3 (.mod3)
2. 选择要导入的.mod3文件
3. 配置导入选项
4. 点击"Load MHW MOD3 file"

### 导出MOD3文件
1. 选择要导出的网格对象
2. File > Export > MHW MOD3 (.mod3)
3. 选择保存位置
4. 配置导出选项
5. 点击"Save MHW MOD3 file"

## 已知问题

目前没有已知的兼容性问题。如果遇到问题，请检查：

1. Blender版本是否为4.0或更高
2. 所有依赖文件是否完整
3. 插件是否正确安装和启用

## 技术细节

### 修改的API调用
- `obj.select` → `obj.select_set()`
- `bpy.context.scene.objects.active` → `bpy.context.view_layer.objects.active`
- `bpy.context.scene.objects.link()` → `bpy.context.collection.objects.link()`

### 保持兼容的功能
- 所有原有的导入/导出功能
- 材质和纹理处理
- 骨骼和动画支持
- 自定义属性处理

## 贡献

如果发现任何兼容性问题或有改进建议，请提交issue或pull request。

## 许可证

保持与原插件相同的许可证。

---

**注意:** 这个更新主要针对API兼容性。插件的核心功能和算法保持不变。
