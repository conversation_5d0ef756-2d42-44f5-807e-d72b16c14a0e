# MHW Mod3 Import-Exporter 安装指南 (Blender 4.4.3)

## 系统要求

- **Blender版本**: 4.0.0 或更高版本 (推荐 4.4.3)
- **操作系统**: Windows, macOS, Linux
- **Python**: Blender内置Python (无需额外安装)

## 安装步骤

### 方法1: 通过Blender插件管理器安装

1. **打开Blender 4.4.3**

2. **进入插件设置**
   - 点击 `Edit` > `Preferences...`
   - 或使用快捷键 `Ctrl+Alt+U` (Windows/Linux) 或 `Cmd+,` (macOS)

3. **选择Add-ons标签**
   - 在左侧菜单中点击 `Add-ons`

4. **安装插件**
   - 点击右上角的 `Install...` 按钮
   - 浏览并选择整个插件文件夹或压缩包
   - 点击 `Install Add-on`

5. **启用插件**
   - 在搜索框中输入 "MHW" 或 "Mod3"
   - 找到 "MHW Mod3 Model Importer" 插件
   - 勾选复选框以启用插件

6. **保存设置**
   - 点击左下角的 `Save Preferences` 按钮

### 方法2: 手动安装

1. **找到Blender插件目录**
   - Windows: `%APPDATA%\Blender Foundation\Blender\4.4\scripts\addons\`
   - macOS: `~/Library/Application Support/Blender/4.4/scripts/addons/`
   - Linux: `~/.config/blender/4.4/scripts/addons/`

2. **复制插件文件**
   - 将整个 `Mod3-MHW-Importer-1.5.0` 文件夹复制到上述目录中

3. **重启Blender**

4. **启用插件**
   - 按照方法1的步骤5-6启用插件

## 验证安装

### 检查菜单项

安装成功后，您应该能在以下位置找到新的菜单项：

- **导入**: `File` > `Import` > `MHW MOD3 (.mod3)`
- **导出**: `File` > `Export` > `MHW MOD3 (.mod3)`

### 运行测试脚本

1. 在Blender中打开脚本编辑器 (`Scripting` 工作区)
2. 打开 `test_compatibility.py` 文件
3. 点击 `Run Script` 按钮
4. 检查控制台输出，确保所有测试通过

## 使用方法

### 导入MOD3文件

1. **准备文件**
   - 确保您有有效的 `.mod3` 文件
   - 如果需要纹理，准备相应的 `.mrl3` 文件

2. **导入操作**
   - `File` > `Import` > `MHW MOD3 (.mod3)`
   - 选择 `.mod3` 文件
   - 配置导入选项：
     - `Clear scene before import`: 导入前清空场景
     - `Only import high LOD parts`: 只导入高细节模型
     - `Import Skeleton`: 选择骨骼导入方式
     - `Import Textures`: 导入纹理（需要纹理路径）

3. **点击导入**
   - 点击 `Load MHW MOD3 file` 按钮

### 导出MOD3文件

1. **准备模型**
   - 确保模型具有正确的属性和结构
   - 选择要导出的网格对象

2. **导出操作**
   - `File` > `Export` > `MHW MOD3 (.mod3)`
   - 选择保存位置和文件名
   - 配置导出选项：
     - `Use Custom Normals`: 使用自定义法线
     - `Set Meshparts to Highest LOD`: 设置为最高细节级别
     - 错误处理级别设置

3. **点击导出**
   - 点击 `Save MHW MOD3 file` 按钮

## 故障排除

### 常见问题

1. **插件未出现在菜单中**
   - 检查插件是否已启用
   - 重启Blender
   - 检查Blender版本是否兼容

2. **导入/导出失败**
   - 检查文件路径是否正确
   - 确保文件未被其他程序占用
   - 查看Blender控制台的错误信息

3. **纹理未正确导入**
   - 确保纹理路径设置正确
   - 检查 `.mrl3` 文件是否存在
   - 验证纹理文件格式

### 获取帮助

1. **查看控制台**
   - `Window` > `Toggle System Console` 查看详细错误信息

2. **检查日志**
   - 插件会在控制台输出调试信息

3. **报告问题**
   - 如果遇到bug，请提供：
     - Blender版本
     - 操作系统
     - 错误信息
     - 问题文件（如果可能）

## 更新插件

1. **下载新版本**
2. **卸载旧版本**
   - 在插件管理器中禁用并删除旧版本
3. **安装新版本**
   - 按照上述安装步骤操作

---

**注意**: 如果您从旧版本的Blender升级，建议重新安装插件以确保兼容性。
